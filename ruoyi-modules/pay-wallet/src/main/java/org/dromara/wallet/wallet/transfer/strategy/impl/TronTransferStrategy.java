package org.dromara.wallet.wallet.transfer.strategy.impl;

import lombok.extern.slf4j.Slf4j;
import org.dromara.wallet.config.facade.TronConfigFacade;
import org.dromara.wallet.wallet.helper.TronHelper;
import org.dromara.wallet.wallet.helper.TronHttpApiHelper;
import org.dromara.wallet.wallet.transfer.alert.TronTransferAlertService;
import org.dromara.wallet.wallet.transfer.config.TransactionConfirmationConfig;
import org.dromara.wallet.wallet.transfer.config.TronTransferMonitorConfig;
import org.dromara.wallet.wallet.transfer.dto.*;
import org.dromara.wallet.wallet.transfer.monitor.TronTransferMetrics;
import org.dromara.wallet.wallet.transfer.monitor.TronTransferMonitorReport;
import org.dromara.wallet.wallet.transfer.strategy.AbstractTransferStrategy;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.math.RoundingMode;

/**
 * TRON转账策略实现
 *
 * <p>封装TRON链的转账逻辑，支持以下特性：</p>
 * <ul>
 *   <li>TRX原生代币转账</li>
 *   <li>TRC20合约代币转账</li>
 *   <li>能量代理优化手续费</li>
 *   <li>手续费钱包支持</li>
 *   <li>交易确认和验证</li>
 * </ul>
 *
 * <AUTHOR>
 * @date 2025/7/19
 */
@Slf4j
@Component
public class TronTransferStrategy extends AbstractTransferStrategy {

    // ==================== 依赖注入 ====================

    private final TronHelper tronHelper;
    private final TronConfigFacade tronConfigFacade;
    private final TronHttpApiHelper httpApiService;
    private final RestTemplate restTemplate;

    /**
     * 监控指标收集器
     */
    @Autowired(required = false)
    private TronTransferMetrics transferMetrics;

    /**
     * 告警服务
     */
    @Autowired(required = false)
    private TronTransferAlertService alertService;

    /**
     * 监控配置
     */
    @Autowired(required = false)
    private TronTransferMonitorConfig monitorConfig;

    /**
     * 构造函数
     * 注入依赖并设置事件发布器
     */
    public TronTransferStrategy(TronHelper tronHelper,
                                TronConfigFacade tronConfigFacade,
                                TronHttpApiHelper httpApiService,
                                @Qualifier("tronRestTemplate") RestTemplate restTemplate,
                                ApplicationEventPublisher eventPublisher) {
        this.tronHelper = tronHelper;
        this.tronConfigFacade = tronConfigFacade;
        this.httpApiService = httpApiService;
        this.restTemplate = restTemplate;
        setEventPublisher(eventPublisher);
    }

    @Override
    public String getChainName() {
        return "TRON";
    }

    @Override
    public boolean supports(String chainName) {
        return "TRON".equalsIgnoreCase(chainName) || "TRX".equalsIgnoreCase(chainName);
    }

    // ==================== 抽象方法实现 ====================

    @Override
    protected void executeTransferInternal(TransferRequest request, BlockchainTransferResult result) {
        long startTime = System.currentTimeMillis();
        boolean isNativeToken = isNativeToken(request.getTokenSymbol());
        String transferType = isNativeToken ? "TRX" : "TRC20";
        String txHash = null;

        try {
            // 1. 记录转账开始
            recordTransferStart(transferType);

            // 2. 统一手续费处理（增强错误处理）
            FeeProvisionResult feeResult = handleFeeProvisionWithRetry(request);

            // 3. 执行具体转账逻辑（增强错误处理）
            txHash = executeTransferWithRetry(request, isNativeToken);

            // 4. 异步转账确认（不阻塞转账流程）
            TransactionConfirmationResult confirmationResult = waitForTransactionConfirmationAsyncWithRecord(
                txHash, transferType + "转账", request, result.getTransferRecordId());

            // 5. 设置转账结果
            result.setSuccess(true);
            result.setTxHash(txHash);
            result.setFeeProvided(feeResult.isProvided());
            result.setFeeAmount(feeResult.getAmount());
            result.setFeeTokenSymbol(feeResult.getTokenSymbol());
            result.setWaitedForConfirmation(confirmationResult.isConfirmed());
            result.setConfirmations(confirmationResult.getActualConfirmations());
            result.setConfirmationResult(confirmationResult);
            result.setConfirmationTime(confirmationResult.getEndTime());

            // 6. 记录转账成功
            long transferTime = System.currentTimeMillis() - startTime;
            long confirmationTime = confirmationResult.getConfirmationTimeMs() != null ?
                confirmationResult.getConfirmationTimeMs() : 0L;
            recordTransferSuccess(transferTime, confirmationTime, feeResult.getAmount(), feeResult.isProvided());

            log.info("TRON转账完成: txHash={}, type={}, time={}ms, feeProvided={}, confirmed={}",
                    txHash, transferType, transferTime, feeResult.isProvided(), confirmationResult.isConfirmed());

        } catch (Exception e) {
            // 7. 简化异常处理 - 避免重复日志记录
            long transferTime = System.currentTimeMillis() - startTime;

            // 记录异常指标
            recordTransferFailure("TRANSFER_FAILED", transferTime);

            // 简化日志记录，避免重复
            log.error("TRON转账失败: txHash={}, time={}ms, error={}",
                txHash, transferTime, e.getMessage());

            throw e; // 重新抛出异常，让上层处理
        }
    }

    @Override
    protected String deriveFromAddress(String privateKey) {
        return tronHelper.getAddressFromPrivateKey(privateKey);
    }

    @Override
    protected FeeEstimate estimateTransferFee(TransferRequest request, boolean isNativeToken) {

        // 获取当前价格信息
        BigInteger energyPrice = httpApiService.getEnergyPrice();
        BigInteger bandwidthPrice = httpApiService.getBandwidthPrice();
        long burnEnergyLimit = tronConfigFacade.getMaxEnergyBurn();

        if (isNativeToken) {
            // TRX转账只需要Bandwidth
            long estimatedBandwidth = 268; // TRX转账的标准Bandwidth消耗
            BigDecimal trxNeeded = BigDecimal.valueOf(estimatedBandwidth)
                .multiply(new BigDecimal(bandwidthPrice))
                .divide(BigDecimal.valueOf(1_000_000), 6, RoundingMode.UP);

            return TronFeeEstimate.forTrxTransfer(estimatedBandwidth, trxNeeded);
        } else {
            // TRC20转账需要Energy和Bandwidth
            long estimatedEnergy = 65100; // TRC20转账的标准Energy消耗
            long estimatedBandwidth = 345; // TRC20转账的标准Bandwidth消耗

            // 计算TRX需求量
            BigDecimal energyCost = BigDecimal.valueOf(Math.min(estimatedEnergy, burnEnergyLimit))
                .multiply(new BigDecimal(energyPrice))
                .divide(BigDecimal.valueOf(1_000_000), 6, RoundingMode.UP);

            BigDecimal bandwidthCost = BigDecimal.valueOf(estimatedBandwidth)
                .multiply(new BigDecimal(bandwidthPrice))
                .divide(BigDecimal.valueOf(1_000_000), 6, RoundingMode.UP);

            BigDecimal trxNeeded = energyCost.add(bandwidthCost);

            return TronFeeEstimate.forTrc20Transfer(estimatedEnergy, estimatedBandwidth, trxNeeded);
        }
    }

    @Override
    protected boolean needsFeeSupport(TransferRequest request, String userAddress, FeeEstimate feeEstimate) {
        BigDecimal userTrxBalance = tronHelper.balanceGetNativeForRead(userAddress);
        return userTrxBalance.compareTo(feeEstimate.getNativeTokenNeeded()) < 0;
    }

    @Override
    protected FeeProvisionResult provideFeeSupport(TransferRequest request, String userAddress, FeeEstimate feeEstimate) {
        BigDecimal trxNeeded = feeEstimate.getNativeTokenNeeded();

        log.info("TRON: 开始提供手续费 | address: {} | 预估TRX: {}", userAddress, trxNeeded);

        // 策略1：尝试能量代理API
        if (tronConfigFacade.isEnergyProxyEnabled()) {
            try {
                boolean energyProxySuccess = provideFeeThroughEnergyProxy(userAddress, trxNeeded);
                if (energyProxySuccess) {
                    log.info("TRON: 能量代理API提供手续费成功");
                    return FeeProvisionResult.thirdPartyApiSuccess(trxNeeded, "能量代理");
                }
                log.warn("TRON: 能量代理API提供手续费失败，尝试下一策略");
            } catch (Exception e) {
                log.warn("TRON: 能量代理API异常，尝试下一策略 | error: {}", e.getMessage());
            }
        } else {
            log.info("TRON: 能量代理API未启用，跳过");
        }

        // 策略2：TRX转账（兜底策略）
        try {
            boolean trxSuccess = provideFeeByTrxTransfer(userAddress, trxNeeded);
            if (trxSuccess) {
                log.info("TRON: TRX转账提供手续费成功");
                return FeeProvisionResult.nativeTransferSuccess(trxNeeded, "TRX");
            }
            log.error("TRON: TRX转账提供手续费失败");
        } catch (Exception e) {
            log.error("TRON: TRX转账异常: {}", e.getMessage());
        }

        // 所有策略都失败
        return FeeProvisionResult.failure("所有手续费提供策略都失败，无法为转账提供手续费");
    }

    @Override
    protected void validateTransferRequest(TransferRequest request) {
        // 调用父类基础验证
        super.validateTransferRequest(request);

        // TRON特定验证
        validateTronRequest(request);
    }

    @Override
    protected String getNativeTokenSymbol() {
        return "TRX";
    }

    @Override
    protected TransactionConfirmationConfig getConfirmationConfig(TransferRequest request) {
        // TRON链确认配置：30秒超时，3秒间隔，基于3秒区块时间
        return TransactionConfirmationConfig.tronDefault();
    }

    protected TransactionConfirmationResult confirmTransactionInternal(String txHash, TransactionConfirmationConfig config) {
        try {
            log.debug("TRON: 开始真实交易确认 | txHash: {}, config: {}", txHash, config.getDescription());

            // 调用TronHelper的真实确认逻辑
            TransactionConfirmationResult result = tronHelper.confirmTransaction(txHash, config);

            if (result.isSuccess()) {
                log.info("TRON: 交易确认成功 | txHash: {}, confirmations: {}",
                    txHash, result.getActualConfirmations());
            } else {
                log.warn("TRON: 交易确认失败 | txHash: {}, status: {}, error: {}",
                    txHash, result.getStatus(), result.getErrorMessage());
            }

            return result;

        } catch (Exception e) {
            log.error("TRON: 交易确认异常 | txHash: {}, error: {}", txHash, e.getMessage());
            return TransactionConfirmationResult.failure(txHash,
                org.dromara.wallet.wallet.transfer.enums.TransactionStatus.UNKNOWN,
                "确认过程异常: " + e.getMessage());
        }
    }

    // ==================== 转账执行方法 ====================

    /**
     * 执行TRX原生代币转账
     */
    private String executeTrxTransfer(TransferRequest request) {
        String fromAddress = deriveFromAddress(request.getPrivateKey());

        log.info("TRON原生代币转账开始: from={}, to={}, amount={}",
            fromAddress, request.getToAddress(), request.getAmount());

        String txHash = tronHelper.transferNative(
            fromAddress, request.getPrivateKey(), request.getAmountAsBigDecimal(), request.getToAddress());

        log.info("TRON原生代币转账成功: txHash={}", txHash);
        return txHash;
    }

    /**
     * 执行TRC20合约代币转账
     */
    private String executeTrc20Transfer(TransferRequest request) {
        String fromAddress = deriveFromAddress(request.getPrivateKey());
        String tokenSymbol = request.getTokenSymbol();

        // 获取合约地址
        String contractAddress = tronConfigFacade.getContractAddress(tokenSymbol);
        if (contractAddress == null || contractAddress.trim().isEmpty()) {
            throw new RuntimeException("TRON链不支持代币: " + tokenSymbol);
        }

        log.info("TRON合约代币转账开始: from={}, to={}, token={}, amount={}",
            fromAddress, request.getToAddress(), tokenSymbol, request.getAmount());

        String txHash = tronHelper.transferTokenByContract(
            fromAddress, request.getPrivateKey(), request.getAmountAsBigDecimal(),
            request.getToAddress(), contractAddress, tokenSymbol);

        log.info("TRON合约代币转账成功: txHash={}", txHash);
        return txHash;
    }

    @Override
    public int getPriority() {
        return 10; // TRON策略优先级较高
    }

    @Override
    public boolean isAvailable() {
        try {
            // 检查TronHelper是否可用（可以添加更多检查）
            return tronHelper != null;
        } catch (Exception e) {
            log.warn("TRON转账策略不可用: {}", e.getMessage());
            return false;
        }
    }

    /**
     * 验证TRON转账请求参数
     */
    private void validateTronRequest(TransferRequest request) {
        // 基础验证
        request.validate();

        // TRON特定验证
        String tokenSymbol = request.getTokenSymbol().toUpperCase();

        // 验证代币符号格式
        if (tokenSymbol.length() < 2 || tokenSymbol.length() > 10) {
            throw new IllegalArgumentException("TRON代币符号长度应在2-10个字符之间");
        }

        // 验证地址格式（TRON地址以T开头）
        if (!request.getToAddress().startsWith("T")) {
            throw new IllegalArgumentException("TRON地址格式不正确，应以T开头");
        }

        // 验证金额范围
        BigDecimal amount = request.getAmountAsBigDecimal();
        if (amount.compareTo(new BigDecimal("0.000001")) < 0) {
            throw new IllegalArgumentException("TRON转账金额过小，最小值为0.000001");
        }

        log.debug("TRON转账请求验证通过: token={}, amount={}, to={}",
            request.getTokenSymbol(), request.getAmount(), request.getToAddress());
    }




    @Override
    public String getDescription() {
        return "TRON区块链转账策略，支持TRX和TRC20代币，集成能量代理优化";
    }

    // ==================== 辅助方法 ====================

    /**
     * 通过能量代理API提供手续费
     * 调用第三方能量代理服务为指定地址提供能量，用于支付TRON交易手续费
     *
     * @param userAddress 用户地址
     * @param trxNeeded 预估需要的TRX数量（用于日志记录）
     * @return 是否成功提供能量
     */
    private boolean provideFeeThroughEnergyProxy(String userAddress, BigDecimal trxNeeded) {
        try {
            // 构建请求URL
            String baseUrl = tronConfigFacade.getEnergyProxyUrl();
            String keyParam = tronConfigFacade.getEnergyProxyKey();
            String keyValue = tronConfigFacade.getEnergyProxyValue();
            int hour = tronConfigFacade.getEnergyProxyHour();

            String requestUrl = String.format("%s?%s=%s&address=%s&hour=%d",
                baseUrl, keyParam, keyValue, userAddress, hour);

            log.info("TRON: 调用能量代理API | address: {} | 预估TRX: {} | hour: {}",
                userAddress, trxNeeded, hour);

            // 发送HTTP GET请求
            String response = restTemplate.getForObject(requestUrl, String.class);

            // 解析响应结果
            if (response != null && !response.trim().isEmpty()) {
                try {
                    int result = Integer.parseInt(response.trim());
                    if (result > 0) {
                        log.info("TRON: 能量代理API调用成功 | address: {} | result: {}", userAddress, result);
                        return true;
                    } else {
                        log.warn("TRON: 能量代理API返回失败 | address: {} | result: {}", userAddress, result);
                        return false;
                    }
                } catch (NumberFormatException e) {
                    log.warn("TRON: 能量代理API响应格式错误 | address: {} | response: {}", userAddress, response);
                    return false;
                }
            } else {
                log.warn("TRON: 能量代理API响应为空 | address: {}", userAddress);
                return false;
            }

        } catch (Exception e) {
            log.error("TRON: 能量代理API调用异常 | address: {} | error: {}", userAddress, e.getMessage());
            return false;
        }
    }

    /**
     * 通过TRX转账提供手续费
     */
    private boolean provideFeeByTrxTransfer(String userAddress, BigDecimal trxNeeded) {
        // 获取手续费钱包配置
        String feeWalletAddress = tronConfigFacade.getFeeWalletAddress();
        String feeWalletPrivateKey = tronConfigFacade.getFeeWalletPrivateKey();

        if (feeWalletAddress == null || feeWalletPrivateKey == null) {
            log.error("TRON: 手续费钱包配置缺失");
            return false;
        }

        // 检查手续费钱包TRX余额
        BigDecimal feeWalletBalance = tronHelper.balanceGetNativeForRead(feeWalletAddress);
        if (feeWalletBalance.compareTo(trxNeeded) < 0) {
            log.error("TRON: 手续费钱包余额不足 | needed: {} | balance: {}", trxNeeded, feeWalletBalance);
            return false;
        }

        try {
            // 从手续费钱包转账TRX到用户地址
            String txHash = tronHelper.transferNative(feeWalletAddress, feeWalletPrivateKey, trxNeeded, userAddress);

            // 等待转账确认，使用统一的确认接口
            TransactionConfirmationConfig config = TransactionConfirmationConfig.builder()
                .timeoutSeconds(6)  // 6秒超时，对应原来的6000ms
                .checkIntervalSeconds(2)
                .requiredConfirmations(1)
                .enableConfirmation(true)
                .build();
            TransactionConfirmationResult confirmResult = tronHelper.confirmTransaction(txHash, config);
            boolean verified = confirmResult.isSuccess();

            if (verified) {
                log.debug("手续费提供成功: {} | {} TRX | {}", userAddress, trxNeeded, txHash);
                return true;
            } else {
                log.debug("手续费转账验证失败: {}", txHash);
                return false;
            }
        } catch (Exception e) {
            log.debug("手续费转账异常: {} | {} TRX | {}", userAddress, trxNeeded, e.getMessage());
            return false;
        }
    }

    // ==================== 增强错误处理方法 ====================

    /**
     * 带重试的手续费处理 - 简化版本
     */
    private FeeProvisionResult handleFeeProvisionWithRetry(TransferRequest request) {
        try {
            return handleFeeProvision(request);
        } catch (Exception e) {
            log.warn("TRON手续费处理失败: {}", e.getMessage());
            // 简化处理，不进行重试，直接抛出异常
            throw new RuntimeException("TRON手续费处理失败: " + e.getMessage(), e);
        }
    }

    /**
     * 带重试的转账执行 - 简化版本
     */
    private String executeTransferWithRetry(TransferRequest request, boolean isNativeToken) {
        int maxRetries = 2; // 简化重试次数
        Exception lastException = null;

        for (int attempt = 1; attempt <= maxRetries; attempt++) {
            try {
                if (isNativeToken) {
                    return executeTrxTransfer(request);
                } else {
                    return executeTrc20Transfer(request);
                }
            } catch (Exception e) {
                lastException = e;
                log.warn("TRON转账执行失败，第{}次尝试: error={}", attempt, e.getMessage());

                // 最后一次尝试失败，直接退出
                if (attempt >= maxRetries) {
                    break;
                }

                // 等待后重试
                try {
                    Thread.sleep(2000L * attempt); // 递增等待时间
                } catch (InterruptedException ie) {
                    Thread.currentThread().interrupt();
                    break;
                }
            }
        }

        // 所有重试都失败，直接抛出最后的异常
        throw new RuntimeException("TRON转账失败: " + lastException.getMessage(), lastException);
    }

    // 移除复杂的异常处理方法，简化代码结构

    // ==================== 监控指标记录方法 ====================

    /**
     * 记录转账开始
     */
    private void recordTransferStart(String transferType) {
        if (transferMetrics != null && isMonitoringEnabled()) {
            transferMetrics.recordTransferStart(transferType);
        }
    }

    /**
     * 记录转账成功
     */
    private void recordTransferSuccess(long transferTime, long confirmationTime,
                                     BigDecimal feeAmount, boolean feeProvided) {
        if (transferMetrics != null && isMonitoringEnabled()) {
            transferMetrics.recordTransferSuccess(transferTime, confirmationTime, feeAmount, feeProvided);
        }
    }

    /**
     * 记录转账失败
     */
    private void recordTransferFailure(String exceptionType, long transferTime) {
        if (transferMetrics != null && isMonitoringEnabled()) {
            transferMetrics.recordTransferFailure(exceptionType, transferTime);
        }
    }

    /**
     * 检查是否启用监控
     */
    private boolean isMonitoringEnabled() {
        return monitorConfig != null && monitorConfig.isEnabled();
    }

    /**
     * 检查是否启用告警
     */
    private boolean isAlertingEnabled() {
        return alertService != null && isMonitoringEnabled();
    }

    /**
     * 生成监控报告
     */
    public TronTransferMonitorReport generateMonitorReport() {
        if (transferMetrics != null && isMonitoringEnabled()) {
            return transferMetrics.generateReport();
        }
        return null;
    }

    /**
     * 检查并发送性能告警
     */
    public void checkAndSendPerformanceAlert() {
        if (transferMetrics != null && alertService != null && isMonitoringEnabled()) {
            TronTransferMonitorReport report = transferMetrics.generateReport();
            if (report != null && report.needsAlert()) {
                alertService.sendPerformanceAlert(report);
            }
        }
    }
}
